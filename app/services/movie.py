"""
Movie service for movie recommendations and data management.
"""
import logging
import pandas as pd
import numpy as np
from typing import List, Optional, Set, Dict, Any
from sklearn.metrics.pairwise import cosine_similarity
import uuid
import gc
import time

from ..models.movie import Movie, Genre, Country
from ..core.config import get_settings
from ..services.embedding import embedding_service
from ..services.cache import cache_service
from ..services.external_api import external_api_service
from ..services.milvus import milvus_service
from ..services.dataset import dataset_service
from ..utils.parsing import safe_eval_list
from ..utils.translations import translate_genre, translate_country

settings = get_settings()
logger = logging.getLogger(__name__)

class MovieService:
    """Service for movie recommendations and data management."""

    def __init__(self):
        """Initialize the movie service."""
        self.movies_df = None
        self.overview_embeddings = None
        self.initialized = False
        self._load_cache()

    async def initialize(self):
        """Asynchronous initialization of the service."""
        if self.initialized:
            return
        
        if milvus_service.initialized:
            if milvus_service.check_collections():
                await external_api_service.refresh_kinopoisk_api_keys()
                self.initialized = True
                return
            
            if self.movies_df is None:
                self._load_and_process_movie_data()

            # Generate embeddings if they don't exist or don't match the dataset size
            if self.overview_embeddings is None or len(self.overview_embeddings) != len(self.movies_df):
                self.overview_embeddings = embedding_service.generate_overview_embeddings(self.movies_df)
                self._cache()

            milvus_service.insert_overview_embeddings(self.overview_embeddings)

            await external_api_service.refresh_kinopoisk_api_keys()

            self.initialized = True
        else:
            logger.info("Milvus is not initialized, skipping movie service initialization")

    def _load_cache(self) -> bool:
        """Load embeddings from cache if available."""
        cached_data = cache_service.load_cached_embeddings()
        if cached_data:
            self.overview_embeddings = cached_data.get('overview_embeddings')
            self.movies_df = cached_data.get('movies_df')
            return True
        return False

    def _cache(self):
        """Cache embeddings to disk."""
        cache_service.cache_embeddings({
            'movies_df': self.movies_df,
            'overview_embeddings': self.overview_embeddings
        })

    def _load_and_process_movie_data(self):
        """Load and preprocess TMDB movie data."""
        try:
            # Ensure dataset exists
            if not dataset_service.ensure_dataset_exists():
                raise RuntimeError("Failed to ensure movie dataset exists")

            # Define required columns
            required_columns = [
                'id', 'title', 'release_date', 'revenue', 'status',
                'imdb_id', 'overview', 'tagline', 'genres', 'imdb_rating',
                'poster_path', 'vote_average', 'production_countries'
            ]

            # Load TMDB dataset in chunks with specific columns
            chunks = pd.read_csv(
                settings.MOVIE_DATASET_PATH,
                usecols=required_columns,
                chunksize=settings.CHUNK_SIZE
            )

            # Process chunks
            processed_chunks = []
            total_rows = 0
            filtered_rows = 0

            for chunk in chunks:
                chunk_size_before = len(chunk)
                total_rows += chunk_size_before

                # Only require title and imdb_id to be present
                chunk.dropna(subset=['title', 'imdb_id'], inplace=True)

                # Filter for released movies only (but don't filter by revenue)
                if 'status' in chunk.columns:
                    chunk = chunk[chunk['status'].isin(['Released', 'Post Production', 'In Production']) | chunk['status'].isna()]

                # Drop unnecessary columns
                if 'status' in chunk.columns:
                    chunk.drop(['status'], axis=1, inplace=True)

                if 'revenue' in chunk.columns:
                    chunk.drop(['revenue'], axis=1, inplace=True)

                # Fill missing values
                chunk.fillna({
                    'imdb_rating': 0,
                    'tagline': '',
                    'overview': '',
                    'vote_average': 0.0,
                    'genres': '[]',
                    'production_countries': '[]'
                }, inplace=True)

                # Clean and normalize data
                chunk['genres'] = chunk['genres'].apply(safe_eval_list)
                chunk['production_countries'] = chunk['production_countries'].apply(safe_eval_list)

                chunk_size_after = len(chunk)
                filtered_rows += (chunk_size_before - chunk_size_after)

                processed_chunks.append(chunk)
                logger.info(f"Processed chunk: {chunk_size_after}/{chunk_size_before} rows kept")

                # Force garbage collection after each chunk
                gc.collect()

            # Combine processed chunks
            self.movies_df = pd.concat(processed_chunks, ignore_index=True)

            # Log statistics about the dataset
            logger.info(f"Total rows in dataset: {total_rows}")
            logger.info(f"Rows filtered out: {filtered_rows} ({filtered_rows/total_rows*100:.1f}%)")
            logger.info(f"Final dataset size: {len(self.movies_df)} movies")

            # Clear processed chunks to free memory
            processed_chunks.clear()
            gc.collect()
        except Exception as e:
            logger.error(f"Error loading and processing movie data: {str(e)}")

    async def get_recommendations(
        self,
        user_genres: List[Genre],
        user_favorites: List[Movie],
        limit: int = 20,
        min_rating: float = 7.0,
        user_id: Optional[str] = None
    ) -> List[Movie]:
        """Get movie recommendations based on user preferences and favorite movies content."""
        # Ensure service is initialized
        if not self.initialized:
            await self.initialize()

        # Get previously recommended movies for this user
        previously_recommended = set()
        if user_id:
            previously_recommended = cache_service.get_recommendation_history(user_id)
            logger.info(f"User {user_id} has {len(previously_recommended)} previously recommended movies")

        # Get user favorite IDs
        user_favorite_ids = set()
        if user_favorites:
            user_favorite_ids = {str(movie.imdb_id) for movie in user_favorites if movie.imdb_id}

        # Combine previously recommended and favorites to exclude
        exclude_ids = previously_recommended.union(user_favorite_ids)

        # Try to use Milvus for secommendations if available
        if milvus_service.initialized:
            try:
                # Use Milvus for recommendations
                return await self._get_recommendations_with_milvus(
                    user_genres=user_genres,
                    user_favorites=user_favorites,
                    limit=limit,
                    min_rating=min_rating,
                    user_id=user_id,
                    exclude_ids=exclude_ids
                )
            except Exception as e:
                logger.error(f"Error using Milvus for recommendations: {str(e)}")

        return []

    async def _get_recommendations_with_milvus(
        self,
        user_genres: List[Genre],
        user_favorites: List[Movie],
        limit: int = 20,
        min_rating: float = 7.0,
        user_id: Optional[str] = None,
        exclude_ids: Optional[Set[str]] = None
    ) -> List[Movie]:
        """Get recommendations using Milvus vector database."""
        start_time = time.time()

        favorite_embeddings = []
        # Get favorite movies with valid imdb_ids
        valid_favorites = [favorite for favorite in (user_favorites[10:] or user_favorites) if favorite.imdb_id]

        if valid_favorites:
            favorite_imdb_ids = [favorite.imdb_id for favorite in valid_favorites]
            try:
                cloud_embeddings = milvus_service.get_embeddings_by_imdb_ids(favorite_imdb_ids)

                if cloud_embeddings:
                    favorite_embeddings = list(cloud_embeddings.values())
                else:
                    logger.warning("Failed to retrieve embeddings from Milvus")
            except Exception as e:
                logger.warning(f"Error retrieving embeddings from Milvus: {str(e)}")

        if not favorite_embeddings:
            logger.warning("No user favorites available for recommendations")
            return await self._get_random_recommendations(limit, min_rating, exclude_ids)

        preferred_genres = [user_genre.en for user_genre in user_genres]

        content_results = milvus_service.search_similar_movies(
            query_embeddings=favorite_embeddings,
            limit=limit,
            exclude_ids=exclude_ids,
            min_rating=min_rating,
            preferred_genres=preferred_genres
        )

        logger.info(f"Found {len(content_results)} results from Milvus search")

        if not content_results:
            logger.warning("No results found from Milvus search, falling back to random recommendations")
            return await self._get_random_recommendations(
                limit=limit,
                min_rating=min_rating,
                exclude_ids=exclude_ids
            )


        # If we have a user_id, update their recommendation history
        if user_id and content_results:
            imdb_id_results = [result["imdb_id"] for result in content_results]
            cache_service.update_recommendation_history(user_id, imdb_id_results)

        # Convert to Movie models
        result = []
        for content in content_results:
            movie = await self._convert_to_movie_model(content)
            if movie is not None:  # Only include movies with successful Kinopoisk API search
                result.append(movie)

        end_time = time.time()
        logger.info(f"Generated {len(result)} recommendations using Milvus in {end_time - start_time:.2f}s")
        return result

    async def _get_random_recommendations(
        self,
        limit: int = 20,
        min_rating: float = 7.0,
        exclude_ids: Optional[Set[str]] = None
    ) -> List[Movie]:
        """Get random highly-rated movie recommendations."""
        # Check if movies_df is available
        if self.movies_df is None:
            logger.error("movies_df is None, cannot get random recommendations")
            return []

        try:
            # Filter by rating
            rating_mask = self.movies_df['imdb_rating'] >= min_rating
        except KeyError:
            rating_mask = pd.Series(True, index=self.movies_df.index)

        # Filter out excluded IDs
        if exclude_ids and len(exclude_ids) > 0:
            imdb_ids = self.movies_df['imdb_id'].astype(str)
            mask = rating_mask & (~imdb_ids.isin(exclude_ids))
        else:
            mask = rating_mask

        filtered_df = self.movies_df[mask]

        # Get random sample
        if len(filtered_df) > limit:
            recommendations = filtered_df.sample(n=limit)
        else:
            recommendations = filtered_df

        # Convert to Movie models
        result = []
        for _, row in recommendations.iterrows():
            movie = await self._convert_to_movie_model(row)
            if movie is not None:  # Only include movies with successful Kinopoisk API search
                result.append(movie)

        return result

    async def get_recommendations_by_description(
        self,
        description: str,
        limit: int = 20,
        min_rating: float = 7.0,
        preferred_genres: Optional[List[str]] = None
    ) -> List[Movie]:
        """Get movie recommendations based on text description.

        Args:
            description: Text description to base recommendations on
            limit: Maximum number of recommendations to return
            min_rating: Minimum IMDb rating threshold
            preferred_genres: List of user's preferred genres to filter by

        Returns:
            List of Movie objects matching the description and filters
        """
        logger.info(f"Getting recommendations for description: '{description[:50]}...'")

        # Ensure service is initialized
        if not self.initialized:
            await self.initialize()

        # Encode description
        description_embedding = embedding_service.get_embedding(description)

        # Try to use Milvus for recommendations if available
        if milvus_service.initialized:
            try:
                # Use Milvus for description-based recommendations
                return await self._get_recommendations_by_description_with_milvus(
                    description_embedding=description_embedding,
                    limit=limit,
                    min_rating=min_rating,
                    preferred_genres=preferred_genres
                )
            except Exception as e:
                logger.error(f"Error using Milvus for description-based recommendations: {str(e)}")
                logger.info("Falling back to in-memory description-based recommendation method")
                # Fall back to in-memory method

        # Use in-memory method if Milvus is not available or failed
        return await self._get_recommendations_by_description_in_memory(
            description_embedding=description_embedding,
            limit=limit,
            min_rating=min_rating,
            preferred_genres=preferred_genres
        )

    async def _get_recommendations_by_description_with_milvus(
        self,
        description_embedding: np.ndarray,
        limit: int = 20,
        min_rating: float = 7.0,
        preferred_genres: Optional[List[str]] = None
    ) -> List[Movie]:
        """Get recommendations by description using Milvus vector database.

        Args:
            description_embedding: Embedding vector of the description text
            limit: Maximum number of recommendations to return
            min_rating: Minimum IMDb rating threshold
            preferred_genres: List of user's preferred genres to filter by

        Returns:
            List of Movie objects matching the description and filters
        """
        logger.info("Getting recommendations by description using Milvus")
        start_time = time.time()

        # Use Milvus to search for similar movies
        search_results = milvus_service.search_by_description(
            description_embedding=description_embedding,
            limit=limit * 2,  # Get more results to allow for filtering
            min_rating=min_rating,
            preferred_genres=preferred_genres
        )

        # Log the number of results found
        logger.info(f"Found {len(search_results)} results from description search")

        # If no results were found, return random recommendations
        if not search_results:
            logger.warning("No results found from description search, falling back to random recommendations")
            return await self._get_random_recommendations(
                limit=limit,
                min_rating=min_rating
            )

        # Limit results to the requested number
        filtered_results = [result["imdb_id"] for result in search_results[:limit]]

        # If we have no results, fall back to random recommendations
        if not filtered_results:
            logger.warning("No results found after filtering, falling back to random recommendations")
            return await self._get_random_recommendations(
                limit=limit,
                min_rating=min_rating
            )


        # Convert to Movie models
        result = []
        for imdb_id in filtered_results:
            # Check if movies_df is available
            if self.movies_df is None:
                logger.error("movies_df is None, cannot convert to movie models")
                break
            movie_row = self.movies_df[self.movies_df['imdb_id'] == imdb_id].iloc[0]
            movie = await self._convert_to_movie_model(movie_row)
            if movie is not None:  # Only include movies with successful Kinopoisk API search
                result.append(movie)

        end_time = time.time()
        logger.info(f"Generated {len(result)} recommendations by description using Milvus in {end_time - start_time:.2f}s")
        return result

    async def _get_recommendations_by_description_in_memory(
        self,
        description_embedding: np.ndarray,
        limit: int = 20,
        min_rating: float = 7.0,
        preferred_genres: Optional[List[str]] = None
    ) -> List[Movie]:
        """Get recommendations by description using in-memory calculations (fallback method).

        Args:
            description_embedding: Embedding vector of the description text
            limit: Maximum number of recommendations to return
            min_rating: Minimum IMDb rating threshold
            preferred_genres: List of user's preferred genres to filter by

        Returns:
            List of Movie objects matching the description and filters
        """
        logger.info("Getting recommendations by description using in-memory method")
        start_time = time.time()

        # Check if movies_df is available
        if self.movies_df is None:
            logger.error("movies_df is None, cannot get recommendations by description")
            return []

        # Calculate similarity scores for each movie
        similarity_scores = np.zeros(len(self.movies_df))

        # Process in batches to reduce memory usage
        batch_size = 1000
        total_processed = 0

        for i in range(0, len(self.movies_df), batch_size):
            batch_end = min(i + batch_size, len(self.movies_df))
            batch_indices = list(range(i, batch_end))

            # Create a list of valid movie embeddings and their indices for this batch
            valid_movie_embeddings = []
            valid_indices = []

            # Get the batch of rows
            batch_rows = [row for _, row in self.movies_df.iloc[batch_indices].iterrows()]

            # Collect valid movie embeddings for this batch
            for idx, row in zip(batch_indices, batch_rows):
                if pd.notna(row['imdb_id']) and row['imdb_id'] in self.overview_embeddings:
                    valid_movie_embeddings.append(self.overview_embeddings[row['imdb_id']])
                    valid_indices.append(idx)

            if valid_movie_embeddings:
                try:
                    # Stack all valid movie embeddings for this batch
                    movie_embeddings_array = np.vstack(valid_movie_embeddings)

                    # Calculate similarity in one batch operation
                    similarities = cosine_similarity(movie_embeddings_array, [description_embedding])

                    # Assign scores to the original indices
                    for idx, sim in zip(valid_indices, similarities.flatten()):
                        similarity_scores[idx] = sim

                    total_processed += len(valid_indices)
                except Exception as e:
                    logger.error(f"Error in batch similarity calculation: {str(e)}")
                    # Fall back to individual calculation for this batch
                    for idx, row in zip(valid_indices, [batch_rows[valid_indices.index(idx)] for idx in valid_indices]):
                        try:
                            movie_embedding = self.overview_embeddings[row['imdb_id']]
                            # Calculate similarity with description
                            similarity = cosine_similarity([movie_embedding], [description_embedding])[0][0]
                            similarity_scores[idx] = similarity
                            total_processed += 1
                        except Exception as e2:
                            logger.error(f"Error calculating similarity for movie {row.get('imdb_id', 'unknown')}: {str(e2)}")

        logger.info(f"Calculated description similarity for {total_processed} movies")

        try:
            rating_mask = self.movies_df['imdb_rating'] >= min_rating
        except KeyError:
            rating_mask = pd.Series(True, index=self.movies_df.index)

        # Create a mask for genre filtering if preferred genres are provided
        genre_mask = pd.Series(True, index=self.movies_df.index)
        if preferred_genres and len(preferred_genres) > 0:
            logger.info(f"Filtering by preferred genres: {preferred_genres}")

            # Create a mask for movies that have at least one of the preferred genres
            genre_mask = pd.Series(False, index=self.movies_df.index)

            for idx, row in self.movies_df.iterrows():
                if 'genres' in row and isinstance(row['genres'], list):
                    # Check if any of the preferred genres are in the movie's genres
                    if any(genre in row['genres'] for genre in preferred_genres):
                        genre_mask.iloc[idx] = True

            logger.info(f"Found {genre_mask.sum()} movies matching preferred genres")

        # Combine masks
        combined_mask = rating_mask & genre_mask

        filtered_df = self.movies_df[combined_mask].copy()
        filtered_df['similarity_score'] = similarity_scores[combined_mask]

        # Get top recommendations
        recommendations = filtered_df.nlargest(limit, 'similarity_score')

        logger.info(f"Found {len(recommendations)} recommendations based on description")

        # Convert to Movie models
        result = []
        for _, row in recommendations.iterrows():
            movie = await self._convert_to_movie_model(row)
            if movie is not None:  # Only include movies with successful Kinopoisk API search
                result.append(movie)

        end_time = time.time()
        logger.info(f"Description recommendation process completed in {end_time - start_time:.2f}s")
        return result

    async def _convert_to_movie_model(self, movie_data: pd.Series) -> Optional[Movie]:
        """Convert pandas Series to Movie model with robust error handling.

        Returns None if Kinopoisk API search fails for the movie title.
        """
        movie_id = movie_data.get('id')

        if movie_id is None:
            return None

        try:
            movie_id = str(movie_id)
        except Exception as e:
            logger.error(f"Error converting movie ID to string: {str(e)}")
            return None

        # Check if we have this movie in cache
        cached_movie = cache_service.get_movie_from_cache(str(movie_id))
        if cached_movie:
            logger.debug(f"Using cached movie data for ID: {movie_id}")
            return cached_movie

        # Extract year from release date if available
        year = None
        if pd.notna(movie_data.get('release_date')):
            try:
                year = int(movie_data['release_date'][:4])
            except (ValueError, TypeError, IndexError):
                logger.warning(f"Could not parse year from release date: {movie_data.get('release_date')}")

        # Extract IMDb rating if available
        imdb_rating = None
        if pd.notna(movie_data.get('imdb_rating')):
            try:
                imdb_rating = float(movie_data['imdb_rating'])
            except (ValueError, TypeError):
                logger.warning(f"Could not parse IMDb rating: {movie_data.get('imdb_rating')}")
        elif pd.notna(movie_data.get('vote_average')):
            try:
                imdb_rating = float(movie_data['vote_average'])
            except (ValueError, TypeError):
                logger.warning(f"Could not parse vote average: {movie_data.get('vote_average')}")

        # Build image URL if poster path is available
        image_url = None
        if pd.notna(movie_data.get('poster_path')):
            image_url = f"https://image.tmdb.org/t/p/original{movie_data['poster_path']}"

        # Convert genres to Genre model objects with translations
        genres = None
        if 'genres' in movie_data:
            try:
                # Handle both string and list inputs
                genre_list = movie_data['genres']
                if isinstance(genre_list, str):
                    genre_list = safe_eval_list(genre_list)
                elif not isinstance(genre_list, list):
                    genre_list = []

                genres = [
                    Genre(
                        en=genre,
                        ru=translate_genre(genre)
                    ) for genre in genre_list
                ]
            except Exception as e:
                logger.error(f"Error converting genres: {str(e)}")

        # Convert countries to Country model objects with translations
        countries = None
        if 'production_countries' in movie_data:
            try:
                # Handle both string and list inputs
                country_list = movie_data['production_countries']
                if isinstance(country_list, str):
                    country_list = safe_eval_list(country_list)
                elif not isinstance(country_list, list):
                    country_list = []

                countries = [
                    Country(
                        en=country,
                        ru=translate_country(country)
                    ) for country in country_list
                ]
            except Exception as e:
                logger.error(f"Error converting countries: {str(e)}")

        title = movie_data.get('title')
        title_ru = title
        overview = movie_data.get('overview')
        overview_ru = overview

        # Initialize YouTube video IDs and Kinopoisk data
        youtube_video_id_en = None
        youtube_video_id_ru = None
        kinopoisk_id = None

        if title:
            # Fetch all external data in parallel
            external_data = await external_api_service.fetch_movie_data_parallel(title, year)

            # Extract YouTube video IDs
            youtube_video_id_en = external_data.get("youtube_en")
            youtube_video_id_ru = external_data.get("youtube_ru")

            # Process Kinopoisk data
            kinopoisk_search_result = external_data.get("kinopoisk_data")
            if kinopoisk_search_result:
                kinopoisk_id = str(kinopoisk_search_result.get('filmId'))

                if kinopoisk_search_result.get('nameRu'):
                    title_ru = kinopoisk_search_result.get('nameRu')

                if kinopoisk_search_result.get('description'):
                    overview_ru = kinopoisk_search_result.get('description')

                if kinopoisk_search_result.get('rating'):
                    try:
                        imdb_rating = float(kinopoisk_search_result.get('rating'))
                    except (ValueError, TypeError):
                        pass

                if kinopoisk_search_result.get('year'):
                    try:
                        year = int(kinopoisk_search_result.get('year'))
                    except (ValueError, TypeError):
                        pass

                if image_url is None and kinopoisk_search_result.get('posterUrl'):
                    image_url = kinopoisk_search_result.get('posterUrl')
            else:
                return None
        else:
            logger.warning(f"Title is None, excluding from results")
            return None

        movie = Movie(
            id=str(movie_data.get('id', uuid.uuid4())),
            title={
                'en': title,
                'ru': title_ru
            },
            description={
                'en': overview,
                'ru': overview_ru
            },
            genres=genres,
            countries=countries,
            year=year,
            imdb_rating=imdb_rating,
            imdb_id=movie_data.get('imdb_id', None),
            kinopoisk_id=kinopoisk_id,
            youtube_video_id={
                'en': youtube_video_id_en,
                'ru': youtube_video_id_ru
            },
            image_url=image_url
        )

        # Save to cache
        cache_service.save_movie_to_cache(movie)

        return movie

    def reset_recommendation_history(self, user_id: str) -> None:
        """Reset the recommendation history for a specific user."""
        cache_service.reset_recommendation_history(user_id)


# Create a singleton instance
movie_service = MovieService()
